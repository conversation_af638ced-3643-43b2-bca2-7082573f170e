// This source code is subject to the terms of the Mozilla Public License 2.0 at https://mozilla.org/MPL/2.0/
// © LeviathanCapital

//@version=5
import DevLucem/ZigLib/1 as ZigZag
indicator("CVD Simple", overlay = false, format = format.volume, max_labels_count=200, max_lines_count=50)

// Input Groups
g1 = 'Display Settings'
g2 = 'Large Change Thresholds'
g3 = 'Volume-Price Imbalances'
g4 = 'Additional Settings'
g5 = 'ZigZag Settings'

// Display Settings
cvd_disp = input.string('Candles', 'CVD Display', options = ['Candles', 'Line'], group = g1)
upcol    = input.color(#d1d4dc, 'Primary Colors              ▲', inline = 'udcol', group = g1)
downcol  = input.color(#9598a1f6, '▼', inline = 'udcol', group = g1)

// Large Change Thresholds
show_mult1     = input.bool(true, '',  inline = 'mult1', group = g2)
mult           = input.float(1.5, '>', inline = 'mult1', group = g2, step = 0.1)
upcol_mult1    = input.color(#c8e6c9, '     ▲', inline = 'mult1', group = g2)
downcol_mult1  = input.color(#faa1a4, '▼', inline = 'mult1', group = g2)

show_mult2     = input.bool(true, '', inline = 'mult2', group = g2)
mult2          = input.float(2.5, '>', inline = 'mult2', group = g2, step = 0.1)
upcol_mult2    = input.color(#a5d6a7, '     ▲', inline = 'mult2', group = g2)
downcol_mult2  = input.color(#f77c80, '▼', inline = 'mult2', group = g2)

show_mult3     = input.bool(true, '', inline = 'mult3', group = g2)
mult3          = input.float(3.5, '>', inline = 'mult3', group = g2, step = 0.1)
upcol_mult3    = input.color(#66bb6a, '     ▲', inline = 'mult3', group = g2)
downcol_mult3  = input.color(#f7525f, '▼', inline = 'mult3', group = g2)

// Volume-Price Imbalances
show_imb1 = input.bool(false, 'High Volume + Small Price Change', group = g3, inline = 'imb1')
imb1_col  = input.color(color.orange, '', group = g3, inline = 'imb1')
vimb1     = input.float(1, 'V >', group = g3, inline = 'imb11', step = 0.1)
pimb1     = input.float(0, 'P <', group = g3, inline = 'imb11', step = 0.1)

show_imb2 = input.bool(false, 'Low Volume + Large Price Change', group = g3, inline = 'imb2')
imb2_col  = input.color(color.blue, '', group = g3, inline = 'imb2')
vimb2     = input.float(0, 'V <', group = g3, inline = 'imb22', step = 0.1)
pimb2     = input.float(1, 'P >', group = g3, inline = 'imb22', step = 0.1)

// Additional Settings
color_candles = input.bool(false, 'Color Candles', group = g4)
gradient      = input.bool(false, 'Gradient Coloring', inline = 'g', group = g4)
gr1           = input.color(#f7525f, '', inline = 'g', group = g4)
gr2           = input.color(#ffe0b2, '', inline = 'g', group = g4)
tf            = input.timeframe('1', 'LTF Timeframe', group = g4)
threshtype    = input.string('RELATIVE', 'Threshold Calculation Type', options = ['SMA', 'RELATIVE', 'Z-SCORE'], group = g4)
zlen          = input.int(50, 'Z Length', group = g4)
smalen        = input.int(300, 'SMA Length', group = g4)
rellen        = input.int(20, 'Relative Length', group = g4)

// ZigZag Settings
show_zigzag_cvd = input.bool(true, 'Show CVD ZigZag', group = g5)
show_zigzag_price = input.bool(true, 'Show Price ZigZag', group = g5)
show_zz_lines = input.bool(true, 'Show ZigZag Lines', group = g5)
show_zz_labels = input.bool(true, 'Show ZigZag Labels', group = g5)
show_divergence = input.bool(true, 'Show Divergence Signals', group = g5)
zz_depth      = input.int(12, 'ZigZag Depth', minval=1, step=1, group = g5)
zz_deviation  = input.int(5, 'ZigZag Deviation', minval=1, step=1, group = g5)
zz_backstep   = input.int(2, 'ZigZag Backstep', minval=2, step=1, group = g5)
zz_line_thick = input.int(2, 'Line Thickness', minval=1, maxval=4, group = g5)
zz_upcolor    = input.color(color.lime, 'Bull Color', group = g5)
zz_dncolor    = input.color(color.red, 'Bear Color', group = g5)
zz_lines_transp = input.int(0, "Lines Transparency", group = g5)
zz_labels_transp = input.int(0, "Labels Transparency", group = g5)

// Advanced Divergence Settings
g6 = 'Advanced Divergence'
prd = input.int(5, "Pivot Period", minval=2, maxval=50, group = g6)
maxpp = input.int(10, "Max Pivot Points", minval=1, maxval=20, group = g6)
maxbars = input.int(100, "Max Bars Back", minval=30, maxval=200, group = g6)
searchdiv = input.string("Regular/Hidden", "Divergence Type", options=["Regular", "Hidden", "Regular/Hidden"], group = g6)
dontconfirm = input.bool(false, "Don't Wait for Confirmation", group = g6)
showlimit = input.int(1, "Show Last Divergences", minval=1, maxval=10, group = g6)
div_bull_color = input.color(color.green, 'Bullish Divergence', group = g6)
div_bear_color = input.color(color.red, 'Bearish Divergence', group = g6)
div_hidden_bull_color = input.color(color.blue, 'Hidden Bullish Divergence', group = g6)
div_hidden_bear_color = input.color(color.orange, 'Hidden Bearish Divergence', group = g6)
div_line_style = input.string('Solid', 'Divergence Line Style', options=['Solid', 'Dashed', 'Dotted'], group = g6)
show_div_lines = input.bool(true, 'Show Divergence Lines', group = g6)

zz_label_size = switch input.int(3, "Label Size", minval=1, maxval=5, group = g5)
    1 => size.tiny
    2 => size.small
    3 => size.normal
    4 => size.large
    5 => size.huge
zz_repaint    = input.bool(true, 'Repaint Levels', group = g5)
zz_extend     = input.bool(false, "Extend ZigZag", group = g5)

// Functions
f_mult1(src) =>
    ta.sma(src, smalen) * (mult + 1)
f_mult2(src) =>
    ta.sma(src, smalen) * (mult2 + 1)
f_mult3(src) =>
    ta.sma(src, smalen) * (mult3 + 1)

f_relative(src) =>
    src / ta.sma(src, rellen)

f_zscore(src) =>
    mean    = ta.sma(src, zlen)
    std     = ta.stdev(src, zlen)
    z_score = (src - mean) / std

// Volume Delta and CVD Calculations
[buy_volume, sell_volume] = request.security_lower_tf(syminfo.tickerid, tf, [close>open ? volume : 0, close<open ? volume : 0])

buy_vol       = array.sum(buy_volume)
sell_vol      = array.sum(sell_volume)
delta_vol     = buy_vol - sell_vol
cum_delta_vol = ta.cum(delta_vol)

rposdelta = f_relative(delta_vol>0 ? delta_vol : 0)
rnegdelta = f_relative(delta_vol<0 ? delta_vol : 0)

vdp_sma   = f_mult1(delta_vol>0 ? delta_vol : 0) * 2
vdp_sma2  = f_mult2(delta_vol>0 ? delta_vol : 0) * 3
vdp_sma3  = f_mult3(delta_vol>0 ? delta_vol : 0) * 7

vdn_sma   = f_mult1(delta_vol<0 ? delta_vol : 0) * 2
vdn_sma2  = f_mult2(delta_vol<0 ? delta_vol : 0) * 3
vdn_sma3  = f_mult3(delta_vol<0 ? delta_vol : 0) * 7

// Coloring Logic
VD_COL        = delta_vol > 0 ? upcol : downcol
color bar_col = color.rgb(0,0,0,100)

// Large Change Threshold Coloring
if threshtype=='SMA'
    if show_mult1 and delta_vol>0 and delta_vol>vdp_sma
        VD_COL  := upcol_mult1
        bar_col := VD_COL
    if show_mult2 and delta_vol>0 and delta_vol>vdp_sma2
        VD_COL  := upcol_mult2
        bar_col := VD_COL
    if show_mult3 and delta_vol>0 and delta_vol>vdp_sma3
        VD_COL  := upcol_mult3
        bar_col := VD_COL
    if show_mult1 and delta_vol<0 and delta_vol<vdn_sma
        VD_COL  := downcol_mult1
        bar_col := VD_COL
    if show_mult2 and delta_vol<0 and delta_vol<vdn_sma2
        VD_COL  := downcol_mult2
        bar_col := VD_COL
    if show_mult3 and delta_vol<0 and delta_vol<vdn_sma3
        VD_COL  := downcol_mult3
        bar_col := VD_COL

if threshtype=='RELATIVE'
    if show_mult1 and delta_vol>0 and rposdelta > mult * 1.5
        VD_COL  := upcol_mult1
        bar_col := VD_COL
    if show_mult2 and delta_vol>0 and rposdelta > mult2 * 1.5
        VD_COL  := upcol_mult2
        bar_col := VD_COL
    if show_mult3 and delta_vol>0 and rposdelta > mult3 * 1.5
        VD_COL  := upcol_mult3
        bar_col := VD_COL
    if show_mult1 and delta_vol<0 and -rnegdelta < -mult * 1.5
        VD_COL  := downcol_mult1
        bar_col := VD_COL
    if show_mult2 and delta_vol<0 and -rnegdelta < -mult2 * 1.5
        VD_COL  := downcol_mult2
        bar_col := VD_COL
    if show_mult3 and delta_vol<0 and -rnegdelta < -mult3 * 1.5
        VD_COL  := downcol_mult3
        bar_col := VD_COL

if threshtype=='Z-SCORE'
    if show_mult1 and delta_vol>0 and f_zscore(rposdelta) >= mult
        VD_COL  := upcol_mult1
        bar_col := VD_COL
    if show_mult2 and delta_vol>0 and f_zscore(rposdelta) >= mult2
        VD_COL  := upcol_mult2
        bar_col := VD_COL
    if show_mult3 and delta_vol>0 and f_zscore(rposdelta) >= mult3
        VD_COL  := upcol_mult3
        bar_col := VD_COL
    if show_mult1 and delta_vol<0 and f_zscore(rnegdelta) >= mult
        VD_COL  := downcol_mult1
        bar_col := VD_COL
    if show_mult2 and delta_vol<0 and f_zscore(rnegdelta) >= mult2
        VD_COL  := downcol_mult2
        bar_col := VD_COL
    if show_mult3 and delta_vol<0 and f_zscore(rnegdelta) >= mult3
        VD_COL  := downcol_mult3
        bar_col := VD_COL

// Volume-Price Imbalance Detection
if show_imb1 and f_zscore(math.abs(delta_vol)) > vimb1 and f_zscore(high-low) < pimb1
    VD_COL  := imb1_col
    bar_col := imb1_col

if show_imb2 and f_zscore(math.abs(delta_vol)) < vimb2 and f_zscore(high-low) > pimb2
    VD_COL  := imb2_col
    bar_col := imb2_col

// Gradient Coloring
if gradient
    glen = 50
    VD_COL := color.from_gradient(delta_vol, ta.lowest(delta_vol, glen), ta.highest(delta_vol, glen), gr2, gr1)

// CVD Candle Data - Create proper OHLC for candles
cvd_open = cum_delta_vol[1]
cvd_high = math.max(cum_delta_vol, cum_delta_vol[1])
cvd_low = math.min(cum_delta_vol, cum_delta_vol[1])
cvd_close = cum_delta_vol

// Plotting CVD
cvd_cond = cvd_disp=='Candles'
plotcandle(cvd_cond ? cvd_open : na, cvd_cond ? cvd_high : na, cvd_cond ? cvd_low : na, cvd_cond ? cvd_close : na,
           color = VD_COL, wickcolor = VD_COL, bordercolor = VD_COL, title = 'CVD Candles', editable = false)
plot(cvd_disp=='Line' ? cum_delta_vol : na, color = VD_COL, title = 'CVD Line', editable = false, linewidth = 2)

// Color candles on main chart
barcolor(color_candles and bar_col != color.rgb(0,0,0,100) ? bar_col : na)

// === Price ZigZag Calculation ===
[price_direction, price_z1, price_z2] = ZigZag.zigzag(low, high, zz_depth, zz_deviation, zz_backstep)
var float price_lastPoint = na
string price_nowPoint = ""

if bool(ta.change(price_direction))
    price_lastPoint := price_z1.price[1]

price_nowPoint := price_direction < 0 ? (price_z2.price < price_lastPoint ? "LL" : "HL") : (price_z2.price > price_lastPoint ? "HH" : "LH")

// === CVD ZigZag Calculation ===
[cvd_direction, cvd_z1, cvd_z2] = ZigZag.zigzag(cum_delta_vol, cum_delta_vol, zz_depth, zz_deviation, zz_backstep)
var float cvd_lastPoint = na
string cvd_nowPoint = ""

if bool(ta.change(cvd_direction))
    cvd_lastPoint := cvd_z1.price[1]

cvd_nowPoint := cvd_direction < 0 ? (cvd_z2.price < cvd_lastPoint ? "LL" : "HL") : (cvd_z2.price > cvd_lastPoint ? "HH" : "LH")

// === Simple Divergence Detection (like divergence-for-many) ===
// Simple pivot detection
ph = ta.pivothigh(high, prd, prd)
pl = ta.pivotlow(low, prd, prd)

// Arrays to store pivot positions and values (simplified)
var ph_positions = array.new<int>(maxpp, 0)
var pl_positions = array.new<int>(maxpp, 0)

// Update pivot arrays when new pivots found
if not na(ph)
    array.unshift(ph_positions, bar_index)
    array.pop(ph_positions)

if not na(pl)
    array.unshift(pl_positions, bar_index)
    array.pop(pl_positions)

// Divergence line style conversion
div_style = switch div_line_style
    'Solid' => line.style_solid
    'Dashed' => line.style_dashed
    'Dotted' => line.style_dotted

// Professional divergence detection functions
// Function to check positive regular or negative hidden divergence
positive_regular_positive_hidden_divergence(src, cond) =>
    divlen = 0
    prsc = low  // Use low for bullish divergences
    // Check if indicator is higher than last value or close is higher than last close
    if dontconfirm or src > src[1] or close > close[1]
        startpoint = dontconfirm ? 0 : 1 // don't check last candle
        // Search last pivot points
        for x = 0 to maxpp - 1
            len = bar_index - array.get(pl_positions, x) + prd
            // If we reach non-valued array element or arrived at max bars then stop searching
            if array.get(pl_positions, x) == 0 or len > maxbars
                break
            if len > 5 and ((cond == 1 and src > cum_delta_vol[len] and prsc < low[len]) or (cond == 2 and src < cum_delta_vol[len] and prsc > low[len]))
                divlen := len
                break
    divlen

// Function to check negative regular or positive hidden divergence
negative_regular_negative_hidden_divergence(src, cond) =>
    divlen = 0
    prsc = high  // Use high for bearish divergences
    // Check if indicator is lower than last value or close is lower than last close
    if dontconfirm or src < src[1] or close < close[1]
        startpoint = dontconfirm ? 0 : 1 // don't check last candle
        // Search last pivot points
        for x = 0 to maxpp - 1
            len = bar_index - array.get(ph_positions, x) + prd
            // If we reach non-valued array element or arrived at max bars then stop searching
            if array.get(ph_positions, x) == 0 or len > maxbars
                break
            if len > 5 and ((cond == 1 and src < cum_delta_vol[len] and prsc > high[len]) or (cond == 2 and src > cum_delta_vol[len] and prsc < high[len]))
                divlen := len
                break
    divlen

// Calculate 4 types of divergence if enabled in the options and return divergences in an array
calculate_divs(cond, indicator) =>
    divs = array.new_int(4, 0)
    array.set(divs, 0, cond and (searchdiv == "Regular" or searchdiv == "Regular/Hidden") ? positive_regular_positive_hidden_divergence(indicator, 1) : 0)
    array.set(divs, 1, cond and (searchdiv == "Regular" or searchdiv == "Regular/Hidden") ? negative_regular_negative_hidden_divergence(indicator, 1) : 0)
    array.set(divs, 2, cond and (searchdiv == "Hidden" or searchdiv == "Regular/Hidden")  ? positive_regular_positive_hidden_divergence(indicator, 2) : 0)
    array.set(divs, 3, cond and (searchdiv == "Hidden" or searchdiv == "Regular/Hidden")  ? negative_regular_negative_hidden_divergence(indicator, 2) : 0)
    divs

// Calculate divergences for CVD using professional method
cvd_divs = calculate_divs(show_divergence, cum_delta_vol)

// Arrays to keep divergence lines and labels (like divergence-for-many)
var pos_div_lines = array.new_line(0)
var neg_div_lines = array.new_line(0)
var pos_div_labels = array.new_label(0)
var neg_div_labels = array.new_label(0)

// Variables to track last divergence lines for shifting
var last_pos_div_lines = 0
var last_neg_div_lines = 0
var remove_last_pos_divs = false
var remove_last_neg_divs = false

// Functions to delete old divergence lines and labels
delete_old_pos_div_lines() =>
    if array.size(pos_div_lines) > 0
        for j = 0 to array.size(pos_div_lines) - 1
            line.delete(array.get(pos_div_lines, j))
        array.clear(pos_div_lines)

delete_old_neg_div_lines() =>
    if array.size(neg_div_lines) > 0
        for j = 0 to array.size(neg_div_lines) - 1
            line.delete(array.get(neg_div_lines, j))
        array.clear(neg_div_lines)

delete_old_pos_div_labels() =>
    if array.size(pos_div_labels) > 0
        for j = 0 to array.size(pos_div_labels) - 1
            label.delete(array.get(pos_div_labels, j))
        array.clear(pos_div_labels)

delete_old_neg_div_labels() =>
    if array.size(neg_div_labels) > 0
        for j = 0 to array.size(neg_div_labels) - 1
            label.delete(array.get(neg_div_labels, j))
        array.clear(neg_div_labels)

// Delete last created lines and labels until we meet new PH/PL
delete_last_pos_div_lines_label(n) =>
    if n > 0 and array.size(pos_div_lines) >= n
        asz = array.size(pos_div_lines)
        for j = 1 to n
            line.delete(array.get(pos_div_lines, asz - j))
            array.pop(pos_div_lines)
        if array.size(pos_div_labels) > 0
            label.delete(array.get(pos_div_labels, array.size(pos_div_labels) - 1))
            array.pop(pos_div_labels)

delete_last_neg_div_lines_label(n) =>
    if n > 0 and array.size(neg_div_lines) >= n
        asz = array.size(neg_div_lines)
        for j = 1 to n
            line.delete(array.get(neg_div_lines, asz - j))
            array.pop(neg_div_lines)
        if array.size(neg_div_labels) > 0
            label.delete(array.get(neg_div_labels, array.size(neg_div_labels) - 1))
            array.pop(neg_div_labels)

// Reset divergence tracking when new pivots are found
if pl
    remove_last_pos_divs := false
    last_pos_div_lines := 0
if ph
    remove_last_neg_divs := false
    last_neg_div_lines := 0

// Dynamic divergence detection with label shifting (like divergence-for-many)
startpoint = dontconfirm ? 0 : 1
distances = array.new_int(0)
old_pos_divs_can_be_removed = true
old_neg_divs_can_be_removed = true

// Variables for divergence detection
regular_bullish = false
regular_bearish = false
hidden_bullish = false
hidden_bearish = false

// Process each type of divergence
for div_type = 0 to 3
    divlen = array.get(cvd_divs, div_type)
    if divlen > 0 // divergence detected
        if not array.includes(distances, divlen) // line doesn't exist yet
            array.push(distances, divlen)

            // Create divergence line if enabled
            line new_line = na
            if show_div_lines
                x1 = bar_index - divlen
                y1 = (div_type % 2) == 0 ? low[divlen] : high[divlen]
                x2 = bar_index - startpoint
                y2 = (div_type % 2) == 0 ? low[startpoint] : high[startpoint]
                line_color = div_type == 0 ? div_bull_color : div_type == 1 ? div_bear_color : div_type == 2 ? div_hidden_bull_color : div_hidden_bear_color
                new_line := line.new(x1, y1, x2, y2, xloc=xloc.bar_index, color=line_color, style=div_style, width=2)

            // Handle positive divergences (bullish)
            if (div_type % 2) == 0
                if old_pos_divs_can_be_removed
                    old_pos_divs_can_be_removed := false
                    if not (showlimit == 1) and remove_last_pos_divs
                        delete_last_pos_div_lines_label(last_pos_div_lines)
                        last_pos_div_lines := 0
                    if showlimit == 1
                        delete_old_pos_div_lines()
                        delete_old_pos_div_labels()
                array.push(pos_div_lines, new_line)
                last_pos_div_lines := last_pos_div_lines + 1
                remove_last_pos_divs := true

                // Set divergence flags
                if div_type == 0
                    regular_bullish := true
                else if div_type == 2
                    hidden_bullish := true

            // Handle negative divergences (bearish)
            if (div_type % 2) == 1
                if old_neg_divs_can_be_removed
                    old_neg_divs_can_be_removed := false
                    if not (showlimit == 1) and remove_last_neg_divs
                        delete_last_neg_div_lines_label(last_neg_div_lines)
                        last_neg_div_lines := 0
                    if showlimit == 1
                        delete_old_neg_div_lines()
                        delete_old_neg_div_labels()
                array.push(neg_div_lines, new_line)
                last_neg_div_lines := last_neg_div_lines + 1
                remove_last_neg_divs := true

                // Set divergence flags
                if div_type == 1
                    regular_bearish := true
                else if div_type == 3
                    hidden_bearish := true

// === Price ZigZag Display (Main Panel) ===
if show_zigzag_price
    line price_zz_line = na
    label price_zz_point = na

    if zz_repaint
        if show_zz_lines
            price_zz_line := line.new(price_z1, price_z2, xloc.bar_time, zz_extend ? extend.right : extend.none,color.new(price_direction > 0 ? zz_upcolor : zz_dncolor, zz_lines_transp), width=zz_line_thick, force_overlay=true)
        if show_zz_labels
            price_zz_point := label.new(price_z2, price_nowPoint, xloc.bar_time, yloc.price,  color.new(price_direction < 0 ? zz_upcolor : zz_dncolor, zz_labels_transp),  price_direction > 0 ? label.style_label_down : label.style_label_up,  color.new(price_direction > 0 ? zz_upcolor : zz_dncolor, zz_labels_transp), zz_label_size, force_overlay=true)

        if price_direction == price_direction[1]
            if show_zz_lines
                line.delete(price_zz_line[1])
            if show_zz_labels
                label.delete(price_zz_point[1])
        else
            if show_zz_lines
                line.set_extend(price_zz_line[1], extend.none)
    else
        if price_direction != price_direction[1]
            if show_zz_lines
                price_zz_line := line.new(price_z1[1], price_z2[1], xloc.bar_time, extend.none,color.new(price_direction[1] > 0 ? zz_upcolor : zz_dncolor, zz_lines_transp), width=zz_line_thick, force_overlay=true)
            if show_zz_labels
                price_zz_point := label.new(price_z2[1], price_nowPoint, xloc.bar_time, yloc.price,  color.new(price_direction[1] < 0 ? zz_upcolor : zz_dncolor, zz_labels_transp),  price_direction[1] > 0 ? label.style_label_down : label.style_label_up,  color.new(price_direction[1] > 0 ? zz_upcolor : zz_dncolor, zz_labels_transp), zz_label_size, force_overlay=true)

// === CVD ZigZag Display (Bottom Panel) ===
if show_zigzag_cvd
    line cvd_zz_line = na
    label cvd_zz_point = na

    if zz_repaint
        if show_zz_lines
            cvd_zz_line := line.new(cvd_z1, cvd_z2, xloc.bar_time, zz_extend ? extend.right : extend.none,color.new(cvd_direction > 0 ? zz_upcolor : zz_dncolor, zz_lines_transp), width=zz_line_thick)
        if show_zz_labels
            cvd_zz_point := label.new(cvd_z2, cvd_nowPoint, xloc.bar_time, yloc.price,  color.new(cvd_direction < 0 ? zz_upcolor : zz_dncolor, zz_labels_transp),  cvd_direction > 0 ? label.style_label_down : label.style_label_up,  color.new(cvd_direction > 0 ? zz_upcolor : zz_dncolor, zz_labels_transp), zz_label_size)

        if cvd_direction == cvd_direction[1]
            if show_zz_lines
                line.delete(cvd_zz_line[1])
            if show_zz_labels
                label.delete(cvd_zz_point[1])
        else
            if show_zz_lines
                line.set_extend(cvd_zz_line[1], extend.none)
    else
        if cvd_direction != cvd_direction[1]
            if show_zz_lines
                cvd_zz_line := line.new(cvd_z1[1], cvd_z2[1], xloc.bar_time, extend.none,color.new(cvd_direction[1] > 0 ? zz_upcolor : zz_dncolor, zz_lines_transp), width=zz_line_thick)
            if show_zz_labels
                cvd_zz_point := label.new(cvd_z2[1], cvd_nowPoint, xloc.bar_time, yloc.price,  color.new(cvd_direction[1] < 0 ? zz_upcolor : zz_dncolor, zz_labels_transp),  cvd_direction[1] > 0 ? label.style_label_down : label.style_label_up,  color.new(cvd_direction[1] > 0 ? zz_upcolor : zz_dncolor, zz_labels_transp), zz_label_size)

// Create divergence labels with dynamic shifting
if show_divergence
    // Create labels for positive divergences (bullish)
    if (regular_bullish or hidden_bullish) and old_pos_divs_can_be_removed == false
        label_text = regular_bullish ? "CVD+" : "CVD+H"
        label_color = regular_bullish ? div_bull_color : div_hidden_bull_color
        new_label = label.new(bar_index - startpoint, low[startpoint], label_text,xloc=xloc.bar_index, yloc=yloc.belowbar, color=label_color,style=label.style_label_up, size=size.small)
        array.push(pos_div_labels, new_label)

    // Create labels for negative divergences (bearish)
    if (regular_bearish or hidden_bearish) and old_neg_divs_can_be_removed == false
        label_text = regular_bearish ? "CVD-" : "CVD-H"
        label_color = regular_bearish ? div_bear_color : div_hidden_bear_color
        new_label = label.new(bar_index - startpoint, high[startpoint], label_text,xloc=xloc.bar_index, yloc=yloc.abovebar, color=label_color,style=label.style_label_down, size=size.small)
        array.push(neg_div_labels, new_label)

// === Advanced Divergence Signals ===
// Regular Bullish Divergence
plotshape(series=regular_bullish ? 1 : na, title="Regular Bullish Divergence",
          color=div_bull_color, style=shape.triangleup, size=size.normal,
          location=location.belowbar, force_overlay=true)

// Regular Bearish Divergence
plotshape(series=regular_bearish ? 1 : na, title="Regular Bearish Divergence",
          color=div_bear_color, style=shape.triangledown, size=size.normal,
          location=location.abovebar, force_overlay=true)

// Hidden Bullish Divergence
plotshape(series=hidden_bullish ? 1 : na, title="Hidden Bullish Divergence",
          color=div_hidden_bull_color, style=shape.circle, size=size.small,
          location=location.belowbar, force_overlay=true)

// Hidden Bearish Divergence
plotshape(series=hidden_bearish ? 1 : na, title="Hidden Bearish Divergence",
          color=div_hidden_bear_color, style=shape.circle, size=size.small,
          location=location.abovebar, force_overlay=true)
