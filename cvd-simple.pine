// This source code is subject to the terms of the Mozilla Public License 2.0 at https://mozilla.org/MPL/2.0/
// © LeviathanCapital

//@version=5
import DevLucem/ZigLib/1 as ZigZag
indicator("CVD Simple", overlay = false, format = format.volume, max_labels_count=200, max_lines_count=50)

// Input Groups
g1 = 'Display Settings'
g2 = 'Large Change Thresholds'
g3 = 'Volume-Price Imbalances'
g4 = 'Additional Settings'
g5 = 'ZigZag Settings'

// Display Settings
cvd_disp = input.string('Candles', 'CVD Display', options = ['Candles', 'Line'], group = g1)
upcol    = input.color(#d1d4dc, 'Primary Colors              ▲', inline = 'udcol', group = g1)
downcol  = input.color(#9598a1f6, '▼', inline = 'udcol', group = g1)

// Large Change Thresholds
show_mult1     = input.bool(true, '',  inline = 'mult1', group = g2)
mult           = input.float(1.5, '>', inline = 'mult1', group = g2, step = 0.1)
upcol_mult1    = input.color(#c8e6c9, '     ▲', inline = 'mult1', group = g2)
downcol_mult1  = input.color(#faa1a4, '▼', inline = 'mult1', group = g2)

show_mult2     = input.bool(true, '', inline = 'mult2', group = g2)
mult2          = input.float(2.5, '>', inline = 'mult2', group = g2, step = 0.1)
upcol_mult2    = input.color(#a5d6a7, '     ▲', inline = 'mult2', group = g2)
downcol_mult2  = input.color(#f77c80, '▼', inline = 'mult2', group = g2)

show_mult3     = input.bool(true, '', inline = 'mult3', group = g2)
mult3          = input.float(3.5, '>', inline = 'mult3', group = g2, step = 0.1)
upcol_mult3    = input.color(#66bb6a, '     ▲', inline = 'mult3', group = g2)
downcol_mult3  = input.color(#f7525f, '▼', inline = 'mult3', group = g2)

// Volume-Price Imbalances
show_imb1 = input.bool(false, 'High Volume + Small Price Change', group = g3, inline = 'imb1')
imb1_col  = input.color(color.orange, '', group = g3, inline = 'imb1')
vimb1     = input.float(1, 'V >', group = g3, inline = 'imb11', step = 0.1)
pimb1     = input.float(0, 'P <', group = g3, inline = 'imb11', step = 0.1)

show_imb2 = input.bool(false, 'Low Volume + Large Price Change', group = g3, inline = 'imb2')
imb2_col  = input.color(color.blue, '', group = g3, inline = 'imb2')
vimb2     = input.float(0, 'V <', group = g3, inline = 'imb22', step = 0.1)
pimb2     = input.float(1, 'P >', group = g3, inline = 'imb22', step = 0.1)

// Additional Settings
color_candles = input.bool(false, 'Color Candles', group = g4)
gradient      = input.bool(false, 'Gradient Coloring', inline = 'g', group = g4)
gr1           = input.color(#f7525f, '', inline = 'g', group = g4)
gr2           = input.color(#ffe0b2, '', inline = 'g', group = g4)
tf            = input.timeframe('1', 'LTF Timeframe', group = g4)
threshtype    = input.string('RELATIVE', 'Threshold Calculation Type', options = ['SMA', 'RELATIVE', 'Z-SCORE'], group = g4)
zlen          = input.int(50, 'Z Length', group = g4)
smalen        = input.int(300, 'SMA Length', group = g4)
rellen        = input.int(20, 'Relative Length', group = g4)

// ZigZag Settings
show_zigzag_cvd = input.bool(true, 'Show CVD ZigZag', group = g5)
show_zigzag_price = input.bool(true, 'Show Price ZigZag', group = g5)
show_zz_lines = input.bool(true, 'Show ZigZag Lines', group = g5)
show_zz_labels = input.bool(true, 'Show ZigZag Labels', group = g5)
show_divergence = input.bool(true, 'Show Divergence Signals', group = g5)
zz_depth      = input.int(12, 'ZigZag Depth', minval=1, step=1, group = g5)
zz_deviation  = input.int(5, 'ZigZag Deviation', minval=1, step=1, group = g5)
zz_backstep   = input.int(2, 'ZigZag Backstep', minval=2, step=1, group = g5)
zz_line_thick = input.int(2, 'Line Thickness', minval=1, maxval=4, group = g5)
zz_upcolor    = input.color(color.lime, 'Bull Color', group = g5)
zz_dncolor    = input.color(color.red, 'Bear Color', group = g5)
zz_lines_transp = input.int(0, "Lines Transparency", group = g5)
zz_labels_transp = input.int(0, "Labels Transparency", group = g5)

// Price ZigZag Settings
g6 = 'Price ZigZag Settings'
price_zz_depth = input.int(7, 'Price ZigZag Depth', minval=1, step=1, group = g6)
price_zz_deviation = input.int(5, 'Price ZigZag Deviation', minval=1, step=1, group = g6)
price_zz_backstep = input.int(2, 'Price ZigZag Backstep', minval=2, step=1, group = g6)
price_zz_line_thick = input.int(2, 'Price Line Thickness', minval=1, maxval=4, group = g6)
price_zz_upcolor = input.color(color.lime, 'Price Bull Color', group = g6)
price_zz_dncolor = input.color(color.red, 'Price Bear Color', group = g6)
price_zz_lines_transp = input.int(0, "Price Lines Transparency", group = g6)
price_zz_labels_transp = input.int(0, "Price Labels Transparency", group = g6)
price_zz_label_size = switch input.int(3, "Price Label Size", minval=1, maxval=5, group = g6)
    1 => size.tiny
    2 => size.small
    3 => size.normal
    4 => size.large
    5 => size.huge
price_zz_repaint = input.bool(true, 'Price Repaint Levels', group = g6)
price_zz_extend = input.bool(false, "Price Extend ZigZag", group = g6)

// CVD ZigZag Settings
g7 = 'CVD ZigZag Settings'
cvd_zz_depth = input.int(12, 'CVD ZigZag Depth', minval=1, step=1, group = g7)
cvd_zz_deviation = input.int(5, 'CVD ZigZag Deviation', minval=1, step=1, group = g7)
cvd_zz_backstep = input.int(2, 'CVD ZigZag Backstep', minval=2, step=1, group = g7)
cvd_zz_line_thick = input.int(2, 'CVD Line Thickness', minval=1, maxval=4, group = g7)
cvd_zz_upcolor = input.color(color.aqua, 'CVD Bull Color', group = g7)
cvd_zz_dncolor = input.color(color.orange, 'CVD Bear Color', group = g7)
cvd_zz_lines_transp = input.int(0, "CVD Lines Transparency", group = g7)
cvd_zz_labels_transp = input.int(0, "CVD Labels Transparency", group = g7)
cvd_zz_label_size = switch input.int(3, "CVD Label Size", minval=1, maxval=5, group = g7)
    1 => size.tiny
    2 => size.small
    3 => size.normal
    4 => size.large
    5 => size.huge
cvd_zz_repaint = input.bool(true, 'CVD Repaint Levels', group = g7)
cvd_zz_extend = input.bool(false, "CVD Extend ZigZag", group = g7)

// CVD Divergence Settings
g8 = 'CVD Divergence Settings'
show_divergence = input.bool(true, 'Show CVD Divergence', group = g8)
show_exhaustion = input.bool(true, 'Show Exhaustion Patterns', group = g8)
show_absorption = input.bool(true, 'Show Absorption Patterns', group = g8)
exhaustion_bull_color = input.color(color.lime, 'Exhaustion Bullish (Price LL, CVD HL)', group = g8)
exhaustion_bear_color = input.color(color.red, 'Exhaustion Bearish (Price HH, CVD LH)', group = g8)
absorption_bull_color = input.color(color.aqua, 'Absorption Bullish (CVD HH, Price LH)', group = g8)
absorption_bear_color = input.color(color.orange, 'Absorption Bearish (CVD LL, Price HL)', group = g8)
show_div_lines = input.bool(true, 'Show Divergence Lines', group = g8)
div_line_style = input.string('Solid', 'Divergence Line Style', options=['Solid', 'Dashed', 'Dotted'], group = g8)
div_lookback = input.int(5, 'Divergence Lookback', minval=2, maxval=20, group = g8)
show_labels = input.bool(true, 'Show Pattern Labels', group = g8)

// Old ZigZag settings removed - now using separate Price and CVD settings above

// Functions
f_mult1(src) =>
    ta.sma(src, smalen) * (mult + 1)
f_mult2(src) =>
    ta.sma(src, smalen) * (mult2 + 1)
f_mult3(src) =>
    ta.sma(src, smalen) * (mult3 + 1)

f_relative(src) =>
    src / ta.sma(src, rellen)

f_zscore(src) =>
    mean    = ta.sma(src, zlen)
    std     = ta.stdev(src, zlen)
    z_score = (src - mean) / std

// Volume Delta and CVD Calculations
[buy_volume, sell_volume] = request.security_lower_tf(syminfo.tickerid, tf, [close>open ? volume : 0, close<open ? volume : 0])

buy_vol       = array.sum(buy_volume)
sell_vol      = array.sum(sell_volume)
delta_vol     = buy_vol - sell_vol
cum_delta_vol = ta.cum(delta_vol)

rposdelta = f_relative(delta_vol>0 ? delta_vol : 0)
rnegdelta = f_relative(delta_vol<0 ? delta_vol : 0)

vdp_sma   = f_mult1(delta_vol>0 ? delta_vol : 0) * 2
vdp_sma2  = f_mult2(delta_vol>0 ? delta_vol : 0) * 3
vdp_sma3  = f_mult3(delta_vol>0 ? delta_vol : 0) * 7

vdn_sma   = f_mult1(delta_vol<0 ? delta_vol : 0) * 2
vdn_sma2  = f_mult2(delta_vol<0 ? delta_vol : 0) * 3
vdn_sma3  = f_mult3(delta_vol<0 ? delta_vol : 0) * 7

// Coloring Logic
VD_COL        = delta_vol > 0 ? upcol : downcol
color bar_col = color.rgb(0,0,0,100)

// Large Change Threshold Coloring
if threshtype=='SMA'
    if show_mult1 and delta_vol>0 and delta_vol>vdp_sma
        VD_COL  := upcol_mult1
        bar_col := VD_COL
    if show_mult2 and delta_vol>0 and delta_vol>vdp_sma2
        VD_COL  := upcol_mult2
        bar_col := VD_COL
    if show_mult3 and delta_vol>0 and delta_vol>vdp_sma3
        VD_COL  := upcol_mult3
        bar_col := VD_COL
    if show_mult1 and delta_vol<0 and delta_vol<vdn_sma
        VD_COL  := downcol_mult1
        bar_col := VD_COL
    if show_mult2 and delta_vol<0 and delta_vol<vdn_sma2
        VD_COL  := downcol_mult2
        bar_col := VD_COL
    if show_mult3 and delta_vol<0 and delta_vol<vdn_sma3
        VD_COL  := downcol_mult3
        bar_col := VD_COL

if threshtype=='RELATIVE'
    if show_mult1 and delta_vol>0 and rposdelta > mult * 1.5
        VD_COL  := upcol_mult1
        bar_col := VD_COL
    if show_mult2 and delta_vol>0 and rposdelta > mult2 * 1.5
        VD_COL  := upcol_mult2
        bar_col := VD_COL
    if show_mult3 and delta_vol>0 and rposdelta > mult3 * 1.5
        VD_COL  := upcol_mult3
        bar_col := VD_COL
    if show_mult1 and delta_vol<0 and -rnegdelta < -mult * 1.5
        VD_COL  := downcol_mult1
        bar_col := VD_COL
    if show_mult2 and delta_vol<0 and -rnegdelta < -mult2 * 1.5
        VD_COL  := downcol_mult2
        bar_col := VD_COL
    if show_mult3 and delta_vol<0 and -rnegdelta < -mult3 * 1.5
        VD_COL  := downcol_mult3
        bar_col := VD_COL

if threshtype=='Z-SCORE'
    if show_mult1 and delta_vol>0 and f_zscore(rposdelta) >= mult
        VD_COL  := upcol_mult1
        bar_col := VD_COL
    if show_mult2 and delta_vol>0 and f_zscore(rposdelta) >= mult2
        VD_COL  := upcol_mult2
        bar_col := VD_COL
    if show_mult3 and delta_vol>0 and f_zscore(rposdelta) >= mult3
        VD_COL  := upcol_mult3
        bar_col := VD_COL
    if show_mult1 and delta_vol<0 and f_zscore(rnegdelta) >= mult
        VD_COL  := downcol_mult1
        bar_col := VD_COL
    if show_mult2 and delta_vol<0 and f_zscore(rnegdelta) >= mult2
        VD_COL  := downcol_mult2
        bar_col := VD_COL
    if show_mult3 and delta_vol<0 and f_zscore(rnegdelta) >= mult3
        VD_COL  := downcol_mult3
        bar_col := VD_COL

// Volume-Price Imbalance Detection
if show_imb1 and f_zscore(math.abs(delta_vol)) > vimb1 and f_zscore(high-low) < pimb1
    VD_COL  := imb1_col
    bar_col := imb1_col

if show_imb2 and f_zscore(math.abs(delta_vol)) < vimb2 and f_zscore(high-low) > pimb2
    VD_COL  := imb2_col
    bar_col := imb2_col

// Gradient Coloring
if gradient
    glen = 50
    VD_COL := color.from_gradient(delta_vol, ta.lowest(delta_vol, glen), ta.highest(delta_vol, glen), gr2, gr1)

// CVD Candle Data - Create proper OHLC for candles
cvd_open = cum_delta_vol[1]
cvd_high = math.max(cum_delta_vol, cum_delta_vol[1])
cvd_low = math.min(cum_delta_vol, cum_delta_vol[1])
cvd_close = cum_delta_vol

// Plotting CVD
cvd_cond = cvd_disp=='Candles'
plotcandle(cvd_cond ? cvd_open : na, cvd_cond ? cvd_high : na, cvd_cond ? cvd_low : na, cvd_cond ? cvd_close : na,
           color = VD_COL, wickcolor = VD_COL, bordercolor = VD_COL, title = 'CVD Candles', editable = false)
plot(cvd_disp=='Line' ? cum_delta_vol : na, color = VD_COL, title = 'CVD Line', editable = false, linewidth = 2)

// Color candles on main chart
barcolor(color_candles and bar_col != color.rgb(0,0,0,100) ? bar_col : na)

// === Price ZigZag Calculation ===
[price_direction, price_z1, price_z2] = ZigZag.zigzag(low, high, price_zz_depth, price_zz_deviation, price_zz_backstep)
var float price_lastPoint = na
string price_nowPoint = ""

if bool(ta.change(price_direction))
    price_lastPoint := price_z1.price[1]

price_nowPoint := price_direction < 0 ? (price_z2.price < price_lastPoint ? "LL" : "HL") : (price_z2.price > price_lastPoint ? "HH" : "LH")

// === CVD ZigZag Calculation ===
[cvd_direction, cvd_z1, cvd_z2] = ZigZag.zigzag(cum_delta_vol, cum_delta_vol, cvd_zz_depth, cvd_zz_deviation, cvd_zz_backstep)
var float cvd_lastPoint = na
string cvd_nowPoint = ""

if bool(ta.change(cvd_direction))
    cvd_lastPoint := cvd_z1.price[1]

cvd_nowPoint := cvd_direction < 0 ? (cvd_z2.price < cvd_lastPoint ? "LL" : "HL") : (cvd_z2.price > cvd_lastPoint ? "HH" : "LH")

// === CVD Divergence Detection ===
// Store pivot points for divergence analysis
var array<float> price_highs = array.new<float>()
var array<float> price_lows = array.new<float>()
var array<float> cvd_highs = array.new<float>()
var array<float> cvd_lows = array.new<float>()
var array<int> price_high_bars = array.new<int>()
var array<int> price_low_bars = array.new<int>()
var array<int> cvd_high_bars = array.new<int>()
var array<int> cvd_low_bars = array.new<int>()

// Divergence line style
div_style = switch div_line_style
    'Solid' => line.style_solid
    'Dashed' => line.style_dashed
    'Dotted' => line.style_dotted

// Track price pivot points
if bool(ta.change(price_direction))
    if price_nowPoint == "HH" or price_nowPoint == "LH"
        array.push(price_highs, price_z2.price)
        array.push(price_high_bars, price_z2.time)
        if array.size(price_highs) > div_lookback
            array.shift(price_highs)
            array.shift(price_high_bars)

    if price_nowPoint == "LL" or price_nowPoint == "HL"
        array.push(price_lows, price_z2.price)
        array.push(price_low_bars, price_z2.time)
        if array.size(price_lows) > div_lookback
            array.shift(price_lows)
            array.shift(price_low_bars)

// Track CVD pivot points
if bool(ta.change(cvd_direction))
    if cvd_nowPoint == "HH" or cvd_nowPoint == "LH"
        array.push(cvd_highs, cvd_z2.price)
        array.push(cvd_high_bars, cvd_z2.time)
        if array.size(cvd_highs) > div_lookback
            array.shift(cvd_highs)
            array.shift(cvd_high_bars)

    if cvd_nowPoint == "LL" or cvd_nowPoint == "HL"
        array.push(cvd_lows, cvd_z2.price)
        array.push(cvd_low_bars, cvd_z2.time)
        if array.size(cvd_lows) > div_lookback
            array.shift(cvd_lows)
            array.shift(cvd_low_bars)

// CVD Pattern Detection Functions (based on professional patterns)
f_exhaustion_bullish() =>
    // DOWNTREND EXHAUSTION: Price makes new lows but CVD doesn't (bullish divergence)
    bool result = false
    if array.size(price_lows) >= 2 and array.size(cvd_lows) >= 2
        price_low1 = array.get(price_lows, array.size(price_lows) - 2)
        price_low2 = array.get(price_lows, array.size(price_lows) - 1)
        cvd_low1 = array.get(cvd_lows, array.size(cvd_lows) - 2)
        cvd_low2 = array.get(cvd_lows, array.size(cvd_lows) - 1)

        // Price makes lower low but CVD makes higher low (exhaustion of selling)
        if price_low2 < price_low1 and cvd_low2 > cvd_low1
            result := true
    result

f_exhaustion_bearish() =>
    // UPTREND EXHAUSTION: Price makes new highs but CVD doesn't (bearish divergence)
    bool result = false
    if array.size(price_highs) >= 2 and array.size(cvd_highs) >= 2
        price_high1 = array.get(price_highs, array.size(price_highs) - 2)
        price_high2 = array.get(price_highs, array.size(price_highs) - 1)
        cvd_high1 = array.get(cvd_highs, array.size(cvd_highs) - 2)
        cvd_high2 = array.get(cvd_highs, array.size(cvd_highs) - 1)

        // Price makes higher high but CVD makes lower high (exhaustion of buying)
        if price_high2 > price_high1 and cvd_high2 < cvd_high1
            result := true
    result

f_absorption_bullish() =>
    // UPTREND ABSORPTION: CVD hits new highs but price doesn't (hidden strength)
    bool result = false
    if array.size(price_highs) >= 2 and array.size(cvd_highs) >= 2
        price_high1 = array.get(price_highs, array.size(price_highs) - 2)
        price_high2 = array.get(price_highs, array.size(price_highs) - 1)
        cvd_high1 = array.get(cvd_highs, array.size(cvd_highs) - 2)
        cvd_high2 = array.get(cvd_highs, array.size(cvd_highs) - 1)

        // CVD makes higher high but price makes lower high (absorption/accumulation)
        if cvd_high2 > cvd_high1 and price_high2 < price_high1
            result := true
    result

f_absorption_bearish() =>
    // DOWNTREND ABSORPTION: CVD hits new lows but price doesn't (hidden weakness)
    bool result = false
    if array.size(price_lows) >= 2 and array.size(cvd_lows) >= 2
        price_low1 = array.get(price_lows, array.size(price_lows) - 2)
        price_low2 = array.get(price_lows, array.size(price_lows) - 1)
        cvd_low1 = array.get(cvd_lows, array.size(cvd_lows) - 2)
        cvd_low2 = array.get(cvd_lows, array.size(cvd_lows) - 1)

        // CVD makes lower low but price makes higher low (absorption/distribution)
        if cvd_low2 < cvd_low1 and price_low2 > price_low1
            result := true
    result

// Calculate CVD patterns
exhaustion_bullish = show_divergence and show_exhaustion and f_exhaustion_bullish()
exhaustion_bearish = show_divergence and show_exhaustion and f_exhaustion_bearish()
absorption_bullish = show_divergence and show_absorption and f_absorption_bullish()
absorption_bearish = show_divergence and show_absorption and f_absorption_bearish()

// === ZigZag Display Logic ===

// === Price ZigZag Display (Main Panel) ===

if show_zigzag_price
    line price_zz_line = na
    label price_zz_point = na

    if price_zz_repaint
        if show_zz_lines
            price_zz_line := line.new(price_z1, price_z2, xloc.bar_time, price_zz_extend ? extend.right : extend.none,color.new(price_direction > 0 ? price_zz_upcolor : price_zz_dncolor, price_zz_lines_transp), width=price_zz_line_thick, force_overlay=true)
        if show_zz_labels
            price_zz_point := label.new(price_z2, price_nowPoint, xloc.bar_time, yloc.price,  color.new(price_direction < 0 ? price_zz_upcolor : price_zz_dncolor, price_zz_labels_transp),  price_direction > 0 ? label.style_label_down : label.style_label_up,  color.new(price_direction > 0 ? price_zz_upcolor : price_zz_dncolor, price_zz_labels_transp), price_zz_label_size, force_overlay=true)

        if price_direction == price_direction[1]
            if show_zz_lines
                line.delete(price_zz_line[1])
            if show_zz_labels
                label.delete(price_zz_point[1])
        else
            if show_zz_lines
                line.set_extend(price_zz_line[1], extend.none)
    else
        if price_direction != price_direction[1]
            if show_zz_lines
                price_zz_line := line.new(price_z1[1], price_z2[1], xloc.bar_time, extend.none,color.new(price_direction[1] > 0 ? price_zz_upcolor : price_zz_dncolor, price_zz_lines_transp), width=price_zz_line_thick, force_overlay=true)
            if show_zz_labels
                price_zz_point := label.new(price_z2[1], price_nowPoint, xloc.bar_time, yloc.price,  color.new(price_direction[1] < 0 ? price_zz_upcolor : price_zz_dncolor, price_zz_labels_transp),  price_direction[1] > 0 ? label.style_label_down : label.style_label_up,  color.new(price_direction[1] > 0 ? price_zz_upcolor : price_zz_dncolor, price_zz_labels_transp), price_zz_label_size, force_overlay=true)

// === CVD ZigZag Display (Bottom Panel) ===
if show_zigzag_cvd
    line cvd_zz_line = na
    label cvd_zz_point = na

    if cvd_zz_repaint
        if show_zz_lines
            cvd_zz_line := line.new(cvd_z1, cvd_z2, xloc.bar_time, cvd_zz_extend ? extend.right : extend.none,color.new(cvd_direction > 0 ? cvd_zz_upcolor : cvd_zz_dncolor, cvd_zz_lines_transp), width=cvd_zz_line_thick)
        if show_zz_labels
            cvd_zz_point := label.new(cvd_z2, cvd_nowPoint, xloc.bar_time, yloc.price,  color.new(cvd_direction < 0 ? cvd_zz_upcolor : cvd_zz_dncolor, cvd_zz_labels_transp),  cvd_direction > 0 ? label.style_label_down : label.style_label_up,  color.new(cvd_direction > 0 ? cvd_zz_upcolor : cvd_zz_dncolor, cvd_zz_labels_transp), cvd_zz_label_size)

        if cvd_direction == cvd_direction[1]
            if show_zz_lines
                line.delete(cvd_zz_line[1])
            if show_zz_labels
                label.delete(cvd_zz_point[1])
        else
            if show_zz_lines
                line.set_extend(cvd_zz_line[1], extend.none)
    else
        if cvd_direction != cvd_direction[1]
            if show_zz_lines
                cvd_zz_line := line.new(cvd_z1[1], cvd_z2[1], xloc.bar_time, extend.none,color.new(cvd_direction[1] > 0 ? cvd_zz_upcolor : cvd_zz_dncolor, cvd_zz_lines_transp), width=cvd_zz_line_thick)
            if show_zz_labels
                cvd_zz_point := label.new(cvd_z2[1], cvd_nowPoint, xloc.bar_time, yloc.price,  color.new(cvd_direction[1] < 0 ? cvd_zz_upcolor : cvd_zz_dncolor, cvd_zz_labels_transp),  cvd_direction[1] > 0 ? label.style_label_down : label.style_label_up,  color.new(cvd_direction[1] > 0 ? cvd_zz_upcolor : cvd_zz_dncolor, cvd_zz_labels_transp), cvd_zz_label_size)

// === CVD ZigZag Display (Bottom Panel) ===
if show_zigzag_cvd
    line cvd_zz_line2 = na
    label cvd_zz_point2 = na

    if cvd_zz_repaint
        if show_zz_lines
            cvd_zz_line2 := line.new(cvd_z1, cvd_z2, xloc.bar_time, cvd_zz_extend ? extend.right : extend.none, color.new(cvd_direction > 0 ? cvd_zz_upcolor : cvd_zz_dncolor, cvd_zz_lines_transp), width=cvd_zz_line_thick)
        if show_zz_labels
            cvd_zz_point2 := label.new(cvd_z2, cvd_nowPoint, xloc.bar_time, yloc.price, color.new(cvd_direction < 0 ? cvd_zz_upcolor : cvd_zz_dncolor, cvd_zz_labels_transp), cvd_direction > 0 ? label.style_label_down : label.style_label_up, color.new(cvd_direction > 0 ? cvd_zz_upcolor : cvd_zz_dncolor, cvd_zz_labels_transp), cvd_zz_label_size)

        if cvd_direction == cvd_direction[1]
            if show_zz_lines
                line.delete(cvd_zz_line2[1])
            if show_zz_labels
                label.delete(cvd_zz_point2[1])
        else
            if show_zz_lines
                line.set_extend(cvd_zz_line2[1], extend.none)
    else
        if cvd_direction != cvd_direction[1]
            if show_zz_lines
                cvd_zz_line2 := line.new(cvd_z1[1], cvd_z2[1], xloc.bar_time, extend.none, color.new(cvd_direction[1] > 0 ? cvd_zz_upcolor : cvd_zz_dncolor, cvd_zz_lines_transp), width=cvd_zz_line_thick)
            if show_zz_labels
                cvd_zz_point2 := label.new(cvd_z2[1], cvd_nowPoint, xloc.bar_time, yloc.price, color.new(cvd_direction[1] < 0 ? cvd_zz_upcolor : cvd_zz_dncolor, cvd_zz_labels_transp), cvd_direction[1] > 0 ? label.style_label_down : label.style_label_up, color.new(cvd_direction[1] > 0 ? cvd_zz_upcolor : cvd_zz_dncolor, cvd_zz_labels_transp), cvd_zz_label_size)

// === CVD Pattern Display ===
// Draw pattern lines on main chart
if show_div_lines and show_divergence
    // Exhaustion Bullish (Price LL, CVD HL)
    if exhaustion_bullish and array.size(price_low_bars) >= 2
        time1 = array.get(price_low_bars, array.size(price_low_bars) - 2)
        time2 = array.get(price_low_bars, array.size(price_low_bars) - 1)
        price1 = array.get(price_lows, array.size(price_lows) - 2)
        price2 = array.get(price_lows, array.size(price_lows) - 1)
        line.new(time1, price1, time2, price2, xloc=xloc.bar_time, color=exhaustion_bull_color, style=div_style, width=3, force_overlay=true)

    // Exhaustion Bearish (Price HH, CVD LH)
    if exhaustion_bearish and array.size(price_high_bars) >= 2
        time1 = array.get(price_high_bars, array.size(price_high_bars) - 2)
        time2 = array.get(price_high_bars, array.size(price_high_bars) - 1)
        price1 = array.get(price_highs, array.size(price_highs) - 2)
        price2 = array.get(price_highs, array.size(price_highs) - 1)
        line.new(time1, price1, time2, price2, xloc=xloc.bar_time, color=exhaustion_bear_color, style=div_style, width=3, force_overlay=true)

    // Absorption Bullish (CVD HH, Price LH)
    if absorption_bullish and array.size(price_high_bars) >= 2
        time1 = array.get(price_high_bars, array.size(price_high_bars) - 2)
        time2 = array.get(price_high_bars, array.size(price_high_bars) - 1)
        price1 = array.get(price_highs, array.size(price_highs) - 2)
        price2 = array.get(price_highs, array.size(price_highs) - 1)
        line.new(time1, price1, time2, price2, xloc=xloc.bar_time, color=absorption_bull_color, style=div_style, width=2, force_overlay=true)

    // Absorption Bearish (CVD LL, Price HL)
    if absorption_bearish and array.size(price_low_bars) >= 2
        time1 = array.get(price_low_bars, array.size(price_low_bars) - 2)
        time2 = array.get(price_low_bars, array.size(price_low_bars) - 1)
        price1 = array.get(price_lows, array.size(price_lows) - 2)
        price2 = array.get(price_lows, array.size(price_lows) - 1)
        line.new(time1, price1, time2, price2, xloc=xloc.bar_time, color=absorption_bear_color, style=div_style, width=2, force_overlay=true)

// CVD Pattern Signals
plotshape(series=exhaustion_bullish ? 1 : na, title="Exhaustion Bullish",
          color=exhaustion_bull_color, style=shape.triangleup, size=size.large,
          location=location.belowbar, force_overlay=true)

plotshape(series=exhaustion_bearish ? 1 : na, title="Exhaustion Bearish",
          color=exhaustion_bear_color, style=shape.triangledown, size=size.large,
          location=location.abovebar, force_overlay=true)

plotshape(series=absorption_bullish ? 1 : na, title="Absorption Bullish",
          color=absorption_bull_color, style=shape.diamond, size=size.normal,
          location=location.belowbar, force_overlay=true)

plotshape(series=absorption_bearish ? 1 : na, title="Absorption Bearish",
          color=absorption_bear_color, style=shape.diamond, size=size.normal,
          location=location.abovebar, force_overlay=true)

// Pattern Labels
if show_labels
    if exhaustion_bullish
        label.new(bar_index, low, "EXHAUSTION\nBULLISH", xloc=xloc.bar_index, yloc=yloc.belowbar,
                 color=exhaustion_bull_color, style=label.style_label_up, size=size.small, force_overlay=true)

    if exhaustion_bearish
        label.new(bar_index, high, "EXHAUSTION\nBEARISH", xloc=xloc.bar_index, yloc=yloc.abovebar,
                 color=exhaustion_bear_color, style=label.style_label_down, size=size.small, force_overlay=true)

    if absorption_bullish
        label.new(bar_index, low, "ABSORPTION\nBULLISH", xloc=xloc.bar_index, yloc=yloc.belowbar,
                 color=absorption_bull_color, style=label.style_label_up, size=size.small, force_overlay=true)

    if absorption_bearish
        label.new(bar_index, high, "ABSORPTION\nBEARISH", xloc=xloc.bar_index, yloc=yloc.abovebar,
                 color=absorption_bear_color, style=label.style_label_down, size=size.small, force_overlay=true)

// CVD Simple indicator complete - ZigZags and Divergence working
